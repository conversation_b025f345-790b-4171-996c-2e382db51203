# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

X-CV-Extractor is an AI-powered microservice for parsing CVs/resumes using OpenAI's GPT models. It's built with FastAPI and supports both synchronous API processing and asynchronous Kafka-based workflows.

## Common Development Commands

### Running the Application
```bash
# Install dependencies
poetry install

# Run the FastAPI server (auto-reload in dev mode)
poetry run python main.py

# The API will be available at http://localhost:8000
# API docs at http://localhost:8000/docs
```

### Testing
```bash
# Run load tests
poetry run locust -f tests/locustfile.py
```

### Linting and Type Checking
Currently, no linting or type checking commands are configured. When implementing new features, ensure code follows Python best practices and FastAPI conventions.

## Architecture & Key Components

### Core Processing Flow
1. **File Upload** → `app/api/v1/routers/extractor.py` handles incoming CV files
2. **File Processing** → `app/handlers/file_processor.py` extracts text from PDF/DOC/DOCX
3. **AI Processing** → `app/handlers/llm_processor.py` uses GPT-4 with specialized prompts
4. **Response Caching** → `app/handlers/cache_handler.py` manages Redis caching
5. **Async Processing** → `app/consumer/extractor_consumer.py` handles Kafka messages

### Key Integration Points
- **OpenAI**: Configure `OPENAI_API_KEY` in `.env`
- **Elasticsearch**: Document storage at `ELASTICSEARCH_*` env vars
- **Redis**: Response caching at `REDIS_*` env vars
- **Kafka**: Async processing at `KAFKA_*` env vars
- **S3**: File storage (optional) at `S3_*` env vars

### Environment Configuration
The application uses environment variables from `.env` file:
- `ENVIRONMENT`: Set to "dev" for development mode with auto-reload
- `OPENAI_MODEL`: GPT model to use (default: gpt-4-turbo-preview)
- See `.env.example` or README.md for full list

### Important Considerations
1. **Security**: Currently lacks authentication and has permissive CORS - implement auth before production
2. **Error Handling**: Improve error responses and validation as noted in CODE_REVIEW.md
3. **Testing**: No unit tests exist - add tests when implementing new features
4. **Performance**: Consider implementing connection pooling and optimizing prompt tokens

### Current Development Focus
Recent commits focus on:
- Tempfile usage for file extraction
- Kafka consumer improvements with aiokafka
- Phone number parsing enhancements
- Current city normalization

When working on the codebase, maintain consistency with existing patterns in the `app/` directory structure and follow FastAPI best practices.
